import os
import zipfile

base_dir = "mithaq_project"
os.makedirs(base_dir + "/app/templates", exist_ok=True)
os.makedirs(base_dir + "/app/static/css", exist_ok=True)
os.makedirs(base_dir + "/app/static/images", exist_ok=True)

files = {
    base_dir + "/run.py": '''from app import create_app

app = create_app()

if __name__ == "__main__":
    print("🚀 بدء تشغيل موقع MYTHAQ...")
    print("📍 الموقع متاح على: http://127.0.0.1:5000")
    print("⏹️  للإيقاف اضغط Ctrl+C")
    print("-" * 50)

    try:
        app.run(
            debug=True,
            host='127.0.0.1',
            port=5000,
            use_reloader=True
        )
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        input("اضغط Enter للخروج...")
''',

    base_dir + "/app/__init__.py": '''from flask import Flask
from flask_bootstrap import Bootstrap

def create_app():
    app = Flask(__name__)
    app.config["SECRET_KEY"] = "your_secret_key_here"

    Bootstrap(app)

    from .routes import main
    app.register_blueprint(main)

    return app
''',

    base_dir + "/app/routes.py": '''from flask import Blueprint, render_template, request, redirect, url_for, flash

main = Blueprint("main", __name__)

@main.route("/")
def home():
    return render_template("home.html")

@main.route("/about")
def about():
    return render_template("about.html")

@main.route("/services")
def services():
    return render_template("services.html")

@main.route("/projects")
def projects():
    return render_template("projects.html")

@main.route("/contact", methods=["GET", "POST"])
def contact():
    if request.method == "POST":
        name = request.form.get("name")
        email = request.form.get("email")
        message = request.form.get("message")
        flash("تم استلام رسالتك بنجاح. سنقوم بالتواصل معك قريباً.", "success")
        return redirect(url_for("main.contact"))
    return render_template("contact.html")
''',

    base_dir + "/app/templates/base.html": '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>{% block title %}MYTHAQ Engineering Consultancy - مجموعة ميثاق للأعمال الهندسية والاستثمار{% endblock %}</title>
  <meta name="description" content="مجموعة ميثاق للأعمال الهندسية والاستثمار - نقدم أفضل الحلول المعمارية والهندسية في ليبيا" />
  <meta name="keywords" content="هندسة, معمارية, استشارات, ليبيا, تصميم, إنشاءات" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet" />
  <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet" />
  <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}" />
</head>
<body>
  <nav class="navbar navbar-expand-lg fixed-top" id="mainNavbar">
    <div class="container">
      <a class="navbar-brand" href="{{ url_for('main.home') }}">
        <i class="fas fa-building me-2"></i>MYTHAQ
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navmenu">
        <span class="navbar-toggler-icon"></span>
      </button>

      <div class="collapse navbar-collapse" id="navmenu">
        <ul class="navbar-nav ms-auto">
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('main.home') }}">
              <i class="fas fa-home me-1"></i>الرئيسية
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('main.about') }}">
              <i class="fas fa-users me-1"></i>من نحن
            </a>
          </li>
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="{{ url_for('main.services') }}" role="button" data-bs-toggle="dropdown">
              <i class="fas fa-cogs me-1"></i>خدماتنا
            </a>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="{{ url_for('main.services') }}#architectural">التصميم المعماري</a></li>
              <li><a class="dropdown-item" href="{{ url_for('main.services') }}#supervision">الإشراف والتنفيذ</a></li>
              <li><a class="dropdown-item" href="{{ url_for('main.services') }}#renovation">الترميم والصيانة</a></li>
              <li><a class="dropdown-item" href="{{ url_for('main.services') }}#consultation">الاستشارات الهندسية</a></li>
            </ul>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('main.projects') }}">
              <i class="fas fa-project-diagram me-1"></i>مشاريعنا
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('main.contact') }}">
              <i class="fas fa-envelope me-1"></i>اتصل بنا
            </a>
          </li>
        </ul>
      </div>
    </div>
  </nav>
  <div class="loading-overlay" id="loadingOverlay">
    <div class="loading-spinner"></div>
  </div>
  <main style="padding-top: 80px;">
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        <div class="container mt-3">
          {% for category, message in messages %}
            <div class="alert alert-{{ 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
              <i class="fas fa-{{ 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
              {{ message }}
              <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
          {% endfor %}
        </div>
      {% endif %}
    {% endwith %}
    {% block content %}
    {% endblock %}
  </main>

  <footer class="text-white">
    <div class="container py-5">
      <div class="row">
        <div class="col-lg-4 mb-4">
          <h5 class="gold-text mb-3">
            <i class="fas fa-building me-2"></i>MYTHAQ
          </h5>
          <p class="mb-3">مجموعة ميثاق للأعمال الهندسية والاستثمار - نحول أحلامكم إلى واقع معماري مبهر</p>
          <div class="social-icons">
            <a href="#" title="فيسبوك"><i class="fab fa-facebook-f"></i></a>
            <a href="#" title="إنستغرام"><i class="fab fa-instagram"></i></a>
            <a href="#" title="لينكد إن"><i class="fab fa-linkedin-in"></i></a>
            <a href="https://wa.me/218912345678" title="واتساب"><i class="fab fa-whatsapp"></i></a>
          </div>
        </div>

        <div class="col-lg-2 col-md-6 mb-4">
          <h6 class="gold-text mb-3">روابط سريعة</h6>
          <ul class="list-unstyled">
            <li><a href="{{ url_for('main.home') }}" class="text-white-50 text-decoration-none">الرئيسية</a></li>
            <li><a href="{{ url_for('main.about') }}" class="text-white-50 text-decoration-none">من نحن</a></li>
            <li><a href="{{ url_for('main.services') }}" class="text-white-50 text-decoration-none">خدماتنا</a></li>
            <li><a href="{{ url_for('main.projects') }}" class="text-white-50 text-decoration-none">مشاريعنا</a></li>
          </ul>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
          <h6 class="gold-text mb-3">خدماتنا</h6>
          <ul class="list-unstyled">
            <li class="text-white-50">التصميم المعماري</li>
            <li class="text-white-50">الإشراف والتنفيذ</li>
            <li class="text-white-50">الترميم والصيانة</li>
            <li class="text-white-50">الاستشارات الهندسية</li>
          </ul>
        </div>

        <div class="col-lg-3 mb-4">
          <h6 class="gold-text mb-3">تواصل معنا</h6>
          <div class="text-white-50">
            <p><i class="fas fa-map-marker-alt me-2"></i>طرابلس، ليبيا</p>
            <p><i class="fas fa-phone me-2"></i>+218 91 234 5678</p>
            <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
          </div>
        </div>
      </div>

      <hr class="my-4" style="border-color: rgba(255,255,255,0.1);">

      <div class="row align-items-center">
        <div class="col-md-6">
          <p class="mb-0 text-white-50">© 2025 MYTHAQ Engineering Consultancy. جميع الحقوق محفوظة.</p>
        </div>
        <div class="col-md-6 text-md-end">
          <p class="mb-0 text-white-50">تصميم وتطوير: <span class="gold-text">فريق MYTHAQ التقني</span></p>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>

  <script>
    // Initialize AOS (Animate On Scroll)
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true
    });

    // Hide loading overlay
    window.addEventListener('load', function() {
      const loadingOverlay = document.getElementById('loadingOverlay');
      loadingOverlay.style.opacity = '0';
      setTimeout(() => {
        loadingOverlay.style.display = 'none';
      }, 500);
    });

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
      const navbar = document.getElementById('mainNavbar');
      if (navbar && window.scrollY > 50) {
        navbar.classList.add('scrolled');
      } else if (navbar) {
        navbar.classList.remove('scrolled');
      }
    });
  </script>

  {% block extra_js %}
  {% endblock %}
</body>
</html>
''',

    base_dir + "/app/templates/home.html": '''{% extends "base.html" %}

{% block title %}الرئيسية - MYTHAQ Engineering Consultancy{% endblock %}

{% block content %}
<section class="hero-section">
  <div class="container">
    <div class="row align-items-center min-vh-100">
      <div class="col-lg-6" data-aos="fade-right">
        <div class="hero-content">
          <h1 class="display-4 fw-bold mb-4">
            <span class="gold-text">MYTHAQ</span><br>
            Engineering Consultancy
          </h1>
          <p class="lead mb-4" style="color: rgba(255,255,255,0.9);">
            A Vision Built. An Idea That Lives.
          </p>
          <p class="fs-5 mb-5" style="color: rgba(255,255,255,0.8);">
            مجموعة ميثاق للأعمال الهندسية والاستثمار<br>
            نحول أحلامكم المعمارية إلى واقع مبهر بخبرة وإبداع لا محدود
          </p>
          <div class="d-flex flex-wrap gap-3">
            <a href="{{ url_for('main.services') }}" class="btn btn-gold btn-lg">
              <i class="fas fa-cogs me-2"></i>تعرف على خدماتنا
            </a>
            <a href="{{ url_for('main.projects') }}" class="btn btn-outline-light btn-lg">
              <i class="fas fa-eye me-2"></i>شاهد مشاريعنا
            </a>
          </div>
        </div>
      </div>
      <div class="col-lg-6" data-aos="fade-left">
        <div class="text-center">
          <img src="{{ url_for('static', filename='images/hero-building.jpg') }}"
               alt="WEDYAR Engineering"
               class="img-fluid rounded-3 shadow-lg"
               style="max-height: 500px; object-fit: cover;" />
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Features Section -->
<section class="py-5">
  <div class="container">
    <div class="row text-center mb-5">
      <div class="col-12" data-aos="fade-up">
        <h2 class="display-5 fw-bold gold-text mb-3">لماذا تختار MYTHAQ؟</h2>
        <p class="lead text-muted">نقدم حلولاً هندسية متكاملة تجمع بين الإبداع والجودة والاحترافية</p>
      </div>
    </div>

    <div class="row g-4">
      <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
        <div class="card h-100 text-center border-0 shadow">
          <div class="card-body p-4">
            <div class="feature-icon mb-3">
              <i class="fas fa-drafting-compass fa-3x gold-text"></i>
            </div>
            <h5 class="card-title fw-bold">تصميم إبداعي</h5>
            <p class="card-text text-muted">
              نصمم مشاريع معمارية مبتكرة تجمع بين الجمال والوظيفية لتحقق رؤيتكم المعمارية
            </p>
          </div>
        </div>
      </div>

      <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
        <div class="card h-100 text-center border-0 shadow">
          <div class="card-body p-4">
            <div class="feature-icon mb-3">
              <i class="fas fa-hard-hat fa-3x gold-text"></i>
            </div>
            <h5 class="card-title fw-bold">تنفيذ احترافي</h5>
            <p class="card-text text-muted">
              فريق من المهندسين المحترفين يضمن تنفيذ مشاريعكم بأعلى معايير الجودة والدقة
            </p>
          </div>
        </div>
      </div>

      <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
        <div class="card h-100 text-center border-0 shadow">
          <div class="card-body p-4">
            <div class="feature-icon mb-3">
              <i class="fas fa-clock fa-3x gold-text"></i>
            </div>
            <h5 class="card-title fw-bold">التزام بالمواعيد</h5>
            <p class="card-text text-muted">
              نلتزم بتسليم مشاريعكم في الوقت المحدد مع الحفاظ على أعلى مستويات الجودة
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Stats Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="row text-center">
      <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
        <div class="stat-item">
          <h3 class="display-4 fw-bold gold-text mb-2">50+</h3>
          <p class="text-muted">مشروع مكتمل</p>
        </div>
      </div>
      <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
        <div class="stat-item">
          <h3 class="display-4 fw-bold gold-text mb-2">15+</h3>
          <p class="text-muted">سنة خبرة</p>
        </div>
      </div>
      <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="300">
        <div class="stat-item">
          <h3 class="display-4 fw-bold gold-text mb-2">100+</h3>
          <p class="text-muted">عميل راضٍ</p>
        </div>
      </div>
      <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="400">
        <div class="stat-item">
          <h3 class="display-4 fw-bold gold-text mb-2">24/7</h3>
          <p class="text-muted">دعم فني</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-5">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-8 text-center" data-aos="fade-up">
        <h2 class="display-5 fw-bold mb-4">هل لديك مشروع في ذهنك؟</h2>
        <p class="lead text-muted mb-4">
          تواصل معنا اليوم ودعنا نحول فكرتك إلى واقع معماري مذهل
        </p>
        <a href="{{ url_for('main.contact') }}" class="btn btn-gold btn-lg">
          <i class="fas fa-phone me-2"></i>تواصل معنا الآن
        </a>
      </div>
    </div>
  </div>
</section>
{% endblock %}
''',

    base_dir + "/app/templates/about.html": '''{% extends "base.html" %}
{% block content %}
<h1 class="fw-bold gold-text mb-4">من نحن</h1>
<p>تأسست مجموعة ميثاق للأعمال الهندسية والاستثمار بهدف تقديم أفضل الخدمات الهندسية والمعمارية بجودة واحترافية عالية.</p>
<h3>رؤيتنا</h3>
<p>أن نكون من أفضل الشركات الهندسية في ليبيا والمنطقة.</p>
<h3>رسالتنا</h3>
<p>تقديم خدمات استشارية هندسية تحقق تطلعات عملائنا وتدعم التنمية المستدامة.</p>
<h3>م. علي عمر باكير ابورقيبه - مؤسس الشركة</h3>
<p>مهندس معماري ذو خبرة واسعة في التصميم والتنفيذ.</p>
<img src="{{ url_for('static', filename='images/founder.jpg') }}" alt="م. علي عمر باكير ابورقيبه" class="img-fluid rounded mt-3" style="max-width:300px;" />
{% endblock %}
''',

    base_dir + "/app/templates/services.html": '''{% extends "base.html" %}
{% block content %}
<h1 class="fw-bold gold-text mb-4">خدماتنا</h1>
<ul class="list-group">
  <li class="list-group-item">التصميم المعماري والداخلي</li>
  <li class="list-group-item">الإشراف والتنفيذ</li>
  <li class="list-group-item">الترميم والصيانة</li>
  <li class="list-group-item">الاستشارات الهندسية والاستثمارية</li>
  <li class="list-group-item">تطوير المشاريع السكنية والتجارية</li>
</ul>
{% endblock %}
''',

    base_dir + "/app/templates/projects.html": '''{% extends "base.html" %}
{% block content %}
<h1 class="fw-bold gold-text mb-4">مشاريعنا</h1>
<div class="row">
  <div class="col-md-4 mb-3">
    <div class="card">
      <img src="{{ url_for('static', filename='images/project1.jpg') }}" class="card-img-top" alt="مشروع 1" />
      <div class="card-body">
        <h5 class="card-title">مشروع تجاري</h5>
        <p class="card-text">موقع المشروع: طرابلس</p>
        <p class="card-text">وصف مختصر للمشروع.</p>
      </div>
    </div>
  </div>
</div>
{% endblock %}
''',

    base_dir + "/app/templates/contact.html": '''{% extends "base.html" %}
{% block content %}
<h1 class="fw-bold gold-text mb-4">اتصل بنا</h1>
<form method="POST" action="{{ url_for('main.contact') }}">
  <div class="mb-3">
    <label for="name" class="form-label">الاسم</label>
    <input type="text" class="form-control" id="name" name="name" required />
  </div>
  <div class="mb-3">
    <label for="email" class="form-label">البريد الإلكتروني</label>
    <input type="email" class="form-control" id="email" name="email" required />
  </div>
  <div class="mb-3">
    <label for="message" class="form-label">الرسالة</label>
    <textarea class="form-control" id="message" name="message" rows="5" required></textarea>
  </div>
  <button type="submit" class="btn btn-primary">إرسال</button>
</form>
<section class="mt-4">
  <p><strong>رقم الهاتف:</strong> +218 91 234 5678 (واتساب متاح)</p>
  <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
  <p><strong>موقع المكتب:</strong></p>
  <iframe
    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3153.086538412351!2d13.185253415678304!3d32.88720908091444!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x13a6e101cae5413b%3A0xc6db2fbdcf6e5c34!2sTripoli%2C+Libya!5e0!3m2!1sar!2sly!4v1685528391234!5m2!1sar!2sly"
    width="100%" height="300" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
</section>
{% endblock %}
''',

    base_dir + "/app/static/css/style.css": '''@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

:root {
  --primary-gold: #D4AF37;
  --secondary-gold: #B8860B;
  --dark-gold: #8B7355;
  --light-gold: #F5E6A3;
  --navy-blue: #1e3a8a;
  --light-navy: #3b82f6;
  --dark-gray: #2d3748;
  --light-gray: #f7fafc;
  --white: #ffffff;
  --shadow: 0 10px 25px rgba(0,0,0,0.1);
  --shadow-hover: 0 15px 35px rgba(0,0,0,0.15);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Cairo", "Poppins", sans-serif;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  color: var(--dark-gray);
  line-height: 1.6;
  overflow-x: hidden;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, var(--primary-gold), var(--secondary-gold));
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255,255,255,0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.navbar {
  background: linear-gradient(90deg, var(--white) 0%, rgba(255,255,255,0.95) 100%) !important;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow);
  padding: 1rem 0;
  transition: all 0.3s ease;
}

.navbar.scrolled {
  padding: 0.5rem 0;
  box-shadow: var(--shadow-hover);
}

.navbar-brand {
  font-family: "Cairo", sans-serif;
  font-weight: 900;
  font-size: 2rem;
  background: linear-gradient(45deg, var(--primary-gold), var(--secondary-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none !important;
  transition: all 0.3s ease;
}

.navbar-brand:hover {
  transform: scale(1.05);
}

.nav-link {
  font-weight: 500;
  color: var(--dark-gray) !important;
  margin: 0 0.5rem;
  padding: 0.5rem 1rem !important;
  border-radius: 25px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  background: linear-gradient(45deg, var(--primary-gold), var(--secondary-gold));
  color: white !important;
  transform: translateY(-2px);
}

.gold-text {
  background: linear-gradient(45deg, var(--primary-gold), var(--secondary-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.btn-gold {
  background: linear-gradient(45deg, var(--primary-gold), var(--secondary-gold));
  border: none;
  color: white;
  padding: 12px 30px;
  border-radius: 50px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
}

.btn-gold:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-hover);
  color: white;
}

.btn-gold::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.btn-gold:hover::before {
  left: 100%;
}

.hero-section {
  background: linear-gradient(135deg, var(--navy-blue) 0%, var(--light-navy) 100%);
  color: white;
  padding: 100px 0;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="%23ffffff" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
  background-size: cover;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.card {
  border: none;
  border-radius: 20px;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  overflow: hidden;
  background: white;
}

.card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-hover);
}

.card-img-top {
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card:hover .card-img-top {
  transform: scale(1.1);
}

.list-group-item {
  border: none;
  border-radius: 15px !important;
  margin-bottom: 10px;
  background: white;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.list-group-item:hover {
  transform: translateX(10px);
  box-shadow: var(--shadow);
  background: linear-gradient(45deg, var(--primary-gold), var(--secondary-gold));
  color: white;
}

.list-group-item::before {
  content: '\\f0da';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: all 0.3s ease;
}

.list-group-item:hover::before {
  opacity: 1;
  right: 15px;
}

footer {
  background: linear-gradient(135deg, var(--dark-gray) 0%, var(--navy-blue) 100%);
  color: white !important;
  padding: 40px 0;
  margin-top: 50px;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.8s ease forwards;
}

@media (max-width: 768px) {
  .navbar-brand {
    font-size: 1.5rem;
  }

  .hero-section {
    padding: 60px 0;
  }

  .btn-gold {
    padding: 10px 25px;
    font-size: 0.9rem;
  }
}

.form-control {
  border-radius: 15px;
  border: 2px solid #e2e8f0;
  padding: 15px 20px;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
}

.social-icons a {
  display: inline-block;
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, var(--primary-gold), var(--secondary-gold));
  color: white;
  text-align: center;
  line-height: 50px;
  border-radius: 50%;
  margin: 0 10px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-icons a:hover {
  transform: translateY(-5px) scale(1.1);
  box-shadow: var(--shadow);
  color: white;
}
''',
    base_dir + "/requirements.txt": '''Flask==2.3.3
Flask-Bootstrap4==4.0.2
Werkzeug==3.1.3
Jinja2==3.1.6
''',

    base_dir + "/start_server.bat": '''@echo off
chcp 65001 >nul
title MYTHAQ Engineering Consultancy Server
color 0A
echo ============================================================
echo                MYTHAQ Engineering Consultancy
echo ============================================================
echo.
echo [INFO] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python from https://python.org
    echo.
    pause
    exit /b 1
)
echo [INFO] Python found successfully
echo.
echo [INFO] Installing requirements...
pip install -r requirements.txt
if errorlevel 1 (
    echo [ERROR] Failed to install requirements
    echo.
    pause
    exit /b 1
)
echo.
echo [INFO] Starting MYTHAQ server...
echo [INFO] Server will be available at: http://127.0.0.1:5000
echo [INFO] Press Ctrl+C to stop the server
echo.
python run.py
echo.
echo [INFO] Server stopped
pause
''',

    base_dir + "/START_MYTHAQ.bat": '''@echo off
cd /d "%~dp0"
echo.
echo ============================================
echo    MYTHAQ Engineering Consultancy
echo ============================================
echo.
echo Starting server...
echo Open your browser and go to: http://127.0.0.1:5000
echo.
python.exe run.py
if errorlevel 1 (
    echo.
    echo Error: Could not start server
    echo Make sure Python is installed
    echo.
)
pause
''',

    base_dir + "/run_simple.bat": '''@echo off
echo Starting MYTHAQ server...
python run.py
pause
''',
}

for path, content in files.items():
    with open(path, "w", encoding="utf-8") as f:
        f.write(content)

zip_path = "mithaq_project.zip"
with zipfile.ZipFile(zip_path, "w") as zf:
    for foldername, subfolders, filenames in os.walk(base_dir):
        for filename in filenames:
            filepath = os.path.join(foldername, filename)
            zf.write(filepath)

print("=" * 60)
print("🎉 تم إنشاء مشروع MYTHAQ بنجاح!")
print("=" * 60)
print(f"📁 مجلد المشروع: {base_dir}")
print(f"📦 ملف ZIP: {zip_path}")
print()
print("📋 الملفات المُنشأة:")
print("  ✅ run.py - ملف تشغيل الخادم")
print("  ✅ requirements.txt - متطلبات المشروع")
print("  ✅ START_MYTHAQ.bat - ملف تشغيل رئيسي")
print("  ✅ start_server.bat - ملف تشغيل متقدم")
print("  ✅ run_simple.bat - ملف تشغيل مبسط")
print("  ✅ app/ - مجلد التطبيق")
print("  ✅ templates/ - قوالب HTML")
print("  ✅ static/ - ملفات CSS والصور")
print()
print("🚀 طرق تشغيل الموقع:")
print("   1️⃣ انقر مرتين على START_MYTHAQ.bat")
print("   2️⃣ انقر مرتين على run_simple.bat")
print(f"   3️⃣ cd {base_dir} && python run.py")
print()
print("🌐 الموقع سيكون متاح على: http://127.0.0.1:5000")
print("=" * 60)
