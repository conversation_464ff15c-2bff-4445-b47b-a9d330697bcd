# إعدادات البريد الإلكتروني لموقع MYTHAQ
import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'mithaq_secret_key_2024_production'

    # إعدادات الإنتاج
    DEBUG = False
    TESTING = False
    
    # إعدادات Gmail SMTP
    MAIL_SERVER = 'smtp.gmail.com'
    MAIL_PORT = 587
    MAIL_USE_TLS = True
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME') or '<EMAIL>'
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD') or ''  # App Password
    MAIL_DEFAULT_SENDER = '<EMAIL>'
    MAIL_RECIPIENT = os.environ.get('MAIL_RECIPIENT') or '<EMAIL>'  # البريد المستقبل للرسائل
    
    # إعدادات اللغات
    LANGUAGES = {
        'ar': 'العربية',
        'en': 'English'
    }


