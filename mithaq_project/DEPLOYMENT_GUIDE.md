# دليل رفع موقع ميتاق للاستشارات الهندسية

## 📋 الملفات المطلوبة للرفع:

### الملفات الأساسية:
- `wsgi.py` - نقطة دخول التطبيق
- `requirements.txt` - قائمة المكتبات المطلوبة
- `config.py` - إعدادات التطبيق
- `.htaccess` - إعدادات Apache (إذا كان الـ hosting يستخدم Apache)

### المجلدات:
- `app/` - كود التطبيق الرئيسي
  - `templates/` - ملفات HTML
  - `static/` - الصور والـ CSS والـ JavaScript
  - `__init__.py` - إعدادات Flask
  - `routes.py` - مسارات الموقع

## 🚀 خطوات الرفع:

### 1. تحضير الملفات:
- ارفع جميع الملفات والمجلدات إلى مجلد `public_html` أو المجلد الرئيسي للموقع
- تأكد من رفع المجلد `app` بالكامل مع جميع محتوياته

### 2. تثبيت المكتبات:
```bash
pip install -r requirements.txt
```

### 3. إعداد متغيرات البيئة:
- `SECRET_KEY` - مفتاح سري للتطبيق
- `MAIL_USERNAME` - بريد Gmail للإرسال
- `MAIL_PASSWORD` - App Password من Gmail
- `MAIL_RECIPIENT` - البريد المستقبل للرسائل

### 4. إعدادات خاصة بنوع الـ Hosting:

#### أ) Shared Hosting (cPanel):
- ارفع الملفات عبر File Manager
- استخدم Python App في cPanel لإعداد التطبيق
- اختر `wsgi.py` كنقطة الدخول

#### ب) VPS/Dedicated Server:
- استخدم Gunicorn أو uWSGI
- إعداد Nginx كـ reverse proxy
- تشغيل التطبيق: `gunicorn wsgi:application`

#### ج) Cloud Hosting (Heroku, DigitalOcean):
- أنشئ `Procfile` مع: `web: gunicorn wsgi:application`
- استخدم Git لرفع الكود

## 🔧 إعدادات إضافية:

### إعداد البريد الإلكتروني:
1. تأكد من أن App Password صحيح
2. فعل "Less secure app access" في Gmail (إذا لزم الأمر)
3. اختبر إرسال البريد بعد الرفع

### إعداد الأمان:
- غير `SECRET_KEY` إلى قيمة عشوائية قوية
- تأكد من أن `DEBUG = False` في الإنتاج
- استخدم HTTPS للموقع

## 📞 الدعم:
إذا واجهت مشاكل في الرفع، تحقق من:
- سجلات الأخطاء (error logs) في لوحة التحكم
- إصدار Python المدعوم (يفضل 3.8+)
- صلاحيات الملفات والمجلدات
