#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime

def test_email_sending():
    """اختبار إرسال البريد الإلكتروني"""
    try:
        print("🔄 بدء اختبار إرسال البريد الإلكتروني...")
        
        # إعدادات Gmail SMTP
        smtp_server = "smtp.gmail.com"
        smtp_port = 587
        sender_email = "<EMAIL>"
        sender_password = "ekkuzvnfeycdggfm"  # App Password
        recipient_email = "<EMAIL>"
        
        print(f"📧 إرسال من: {sender_email}")
        print(f"📧 إرسال إلى: {recipient_email}")
        
        # إنشاء الرسالة
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = recipient_email
        msg['Subject'] = "اختبار البريد الإلكتروني - ميتاق"
        
        # محتوى الرسالة
        body = f"""
اختبار البريد الإلكتروني من موقع ميتاق

هذه رسالة اختبار للتأكد من عمل نظام البريد الإلكتروني.

تم الإرسال في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        msg.attach(MIMEText(body, 'plain', 'utf-8'))
        
        # إرسال الإيميل
        print("🔐 الاتصال بخادم Gmail...")
        server = smtplib.SMTP(smtp_server, smtp_port)
        print("🔒 تفعيل التشفير...")
        server.starttls()
        print("🔑 تسجيل الدخول...")
        server.login(sender_email, sender_password)
        print("📤 إرسال الرسالة...")
        text = msg.as_string()
        server.sendmail(sender_email, recipient_email, text)
        server.quit()
        print("✅ تم إرسال الإيميل بنجاح!")
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ خطأ في المصادقة: {e}")
        print("💡 تحقق من App Password أو فعل التحقق بخطوتين")
        return False
    except smtplib.SMTPException as e:
        print(f"❌ خطأ SMTP: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    test_email_sending()
