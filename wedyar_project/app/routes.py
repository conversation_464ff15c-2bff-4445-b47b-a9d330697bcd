from flask import Blueprint, render_template, request, redirect, url_for, flash, session, current_app
from datetime import datetime
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

main = Blueprint("main", __name__)

def send_simple_email(name, email, phone, subject, message):
    """دالة بسيطة لإرسال الإيميل باستخدام SMTP"""
    try:
        print("🔄 بدء عملية إرسال الإيميل...")

        # إعدادات Gmail SMTP
        smtp_server = "smtp.gmail.com"
        smtp_port = 587
        sender_email = "<EMAIL>"
        sender_password = "ivnfwzmaixozgzlc"  # App Password الجديد من Gmail
        recipient_email = current_app.config.get('MAIL_RECIPIENT', '<EMAIL>')

        print(f"📧 إرسال من: {sender_email}")
        print(f"📧 إرسال إلى: {recipient_email}")
        print(f"📧 الموضوع: رسالة جديدة من موقع ميتاق - {subject}")

        # إنشاء الرسالة
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = recipient_email
        msg['Subject'] = f"رسالة جديدة من موقع ميتاق - {subject}"

        # محتوى الرسالة
        body = f"""
رسالة جديدة من موقع ميتاق للاستشارات الهندسية

معلومات المرسل:
الاسم: {name}
البريد الإلكتروني: {email}
رقم الهاتف: {phone}
الموضوع: {subject}

الرسالة:
{message}

---
تم الإرسال في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
من موقع: ميتاق للاستشارات الهندسية
        """

        msg.attach(MIMEText(body, 'plain', 'utf-8'))

        # إرسال الإيميل
        if sender_password:
            print("🔐 الاتصال بخادم Gmail...")
            server = smtplib.SMTP(smtp_server, smtp_port)
            print("🔒 تفعيل التشفير...")
            server.starttls()
            print("🔑 تسجيل الدخول...")
            server.login(sender_email, sender_password)
            print("📤 إرسال الرسالة...")
            text = msg.as_string()
            server.sendmail(sender_email, recipient_email, text)
            server.quit()
            print("✅ تم إرسال الإيميل بنجاح!")
            return True
        else:
            print("❌ لم يتم تعيين كلمة مرور الإيميل")
            return False

    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ خطأ في المصادقة: {e}")
        print("💡 تحقق من App Password أو فعل التحقق بخطوتين")
        return False
    except smtplib.SMTPException as e:
        print(f"❌ خطأ SMTP: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام في إرسال الإيميل: {e}")
        return False

@main.route("/")
def home():
    return render_template("home.html")

@main.route("/about")
def about():
    return render_template("about.html")

@main.route("/services")
def services():
    return render_template("services.html")

@main.route("/projects")
def projects():
    return render_template("projects.html")

@main.route("/team")
def team():
    current_lang = session.get('language', 'ar')

    # بيانات العاملين في الشركة
    if current_lang == 'en':
        team_members = [
            {
                'name': 'Eng. Ahmed Mohammed Al-Mitaq',
                'position': 'CEO & Founder',
                'specialization': 'Architecture & Urban Planning',
                'experience': '15+ years',
                'education': 'Master of Architecture - King Saud University',
                'description': 'Architect specialized in innovative architectural design and sustainable urban planning. Has extensive experience in managing major projects and real estate development.',
                'image': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
                'skills': ['Architectural Design', 'Project Management', 'Urban Planning', 'Engineering Consultation'],
                'projects': '50+ projects',
                'linkedin': '#',
                'email': '<EMAIL>'
            },
            {
               'name': 'Eng. Mohammed Ali',
                'position': 'Architectural Design Manager',
                'specialization': 'Architectural Design & Interior Decoration',
                'experience': '12+ years',
                'education': 'Bachelor of Architecture - King Abdulaziz University',
                'description': 'Architect specialized in modern architectural design and interior decoration. Focuses on combining beauty with functionality in all his projects.',
                'image': 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
                'skills': ['Architectural Design', 'Interior Design', '3D Design', 'Team Management'],
                'projects': '35+ projects',
                'linkedin': '#',
                'email': '<EMAIL>'
            },
            {
                'name': 'Eng. Omar Al-Rashid',
                'position': 'Senior Civil Engineer',
                'specialization': 'Civil Engineering & Infrastructure',
                'experience': '9+ years',
                'education': 'Master of Civil Engineering - King Fahd University',
                'description': 'Civil engineer specialized in infrastructure design and construction management. Expert in roads, bridges, and water systems with focus on sustainable solutions.',
                'image': 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&h=400&fit=crop&crop=face',
                'skills': ['Infrastructure Design', 'Construction Management', 'Project Planning', 'Quality Control'],
                'projects': '30+ projects',
                'linkedin': '#',
                'email': '<EMAIL>'
            },
            {
                'name': 'Eng. Mowayed',
                'position': 'Senior Project Engineer',
                'specialization': 'Engineering Management & Technical Solutions',
                'experience': '10+ years',
                'education': 'Master of Engineering - Technical University',
                'description': 'A distinguished engineer with extensive experience in project management and technical solutions. Known for his analytical approach and commitment to delivering high-quality engineering solutions with precision and professionalism.',
                'image': url_for('static', filename='images/mowayed-photo.jpg'),
                'skills': ['Project Management', 'Technical Analysis', 'Quality Assurance', 'Team Leadership'],
                'projects': '40+ projects',
                'linkedin': '#',
                'email': '<EMAIL>'
            }
        ]
    else:
        team_members = [
        {
            'name': 'م. أحمد محمد الميتاق',
            'position': 'المدير التنفيذي والمؤسس',
            'specialization': 'هندسة معمارية وتخطيط عمراني',
            'experience': '15+ سنة',
            'education': 'ماجستير في الهندسة المعمارية - جامعة الملك سعود',
            'description': 'مهندس معماري متخصص في التصميم المعماري المبتكر والتخطيط العمراني المستدام. يتمتع بخبرة واسعة في إدارة المشاريع الكبرى والتطوير العقاري.',
            'image': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
            'skills': ['التصميم المعماري', 'إدارة المشاريع', 'التخطيط العمراني', 'الاستشارات الهندسية'],
            'projects': '50+ مشروع',
            'linkedin': '#',
            'email': '<EMAIL>'
        },
        {
            'name': 'م. محمد علي',
            'position': 'مدير التصميم المعماري',
            'specialization': 'التصميم المعماري والديكور الداخلي',
            'experience': '12+ سنة',
            'education': 'بكالوريوس هندسة معمارية - جامعة الملك عبدالعزيز',
            'description': 'مهندس معماري متخصص في التصميم المعماري الحديث والديكور الداخلي. يركز على دمج الجمال مع الوظيفية في جميع مشاريعه.',
            'image': 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
            'skills': ['التصميم المعماري', 'الديكور الداخلي', 'التصميم ثلاثي الأبعاد', 'إدارة الفرق'],
            'projects': '35+ مشروع',
            'linkedin': '#',
            'email': '<EMAIL>'
        },
        {
            'name': 'م. عمر الراشد',
            'position': 'مهندس مدني أول',
            'specialization': 'الهندسة المدنية والبنية التحتية',
            'experience': '9+ سنوات',
            'education': 'ماجستير في الهندسة المدنية - جامعة الملك فهد',
            'description': 'مهندس مدني متخصص في تصميم البنية التحتية وإدارة الإنشاءات. خبير في الطرق والجسور وأنظمة المياه مع التركيز على الحلول المستدامة.',
            'image': 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&h=400&fit=crop&crop=face',
            'skills': ['تصميم البنية التحتية', 'إدارة الإنشاءات', 'تخطيط المشاريع', 'مراقبة الجودة'],
            'projects': '30+ مشروع',
            'linkedin': '#',
            'email': '<EMAIL>'
        },
        {
            'name': 'م. مويد',
            'position': 'مهندس مشاريع أول',
            'specialization': 'إدارة المشاريع والحلول التقنية',
            'experience': '10+ سنوات',
            'education': 'ماجستير في الهندسة - الجامعة التقنية',
            'description': 'مهندس متميز ذو خبرة واسعة في إدارة المشاريع والحلول التقنية. يُعرف بنهجه التحليلي والتزامه بتقديم حلول هندسية عالية الجودة بدقة واحترافية.',
            'image': url_for('static', filename='images/mowayed-photo.jpg'),
            'skills': ['إدارة المشاريع', 'التحليل التقني', 'ضمان الجودة', 'قيادة الفرق'],
            'projects': '40+ مشروع',
            'linkedin': '#',
            'email': '<EMAIL>'
        }
    ]

    return render_template("team.html", team_members=team_members)

@main.route("/set_language/<language>")
def set_language(language=None):
    """تغيير لغة الموقع"""
    if language and language in current_app.config['LANGUAGES']:
        session['language'] = language
    return redirect(request.referrer or url_for('main.home'))

@main.route("/contact", methods=["GET", "POST"])
def contact():
    if request.method == "POST":
        name = request.form.get("name", "").strip()
        email = request.form.get("email", "").strip()
        phone = request.form.get("phone", "").strip()
        subject = request.form.get("subject", "").strip()
        message = request.form.get("message", "").strip()

        # التحقق من صحة البيانات من جانب الخادم
        import re

        # التحقق من الحقول المطلوبة
        if not name or not email or not subject or not message:
            flash("يرجى ملء جميع الحقول المطلوبة.", "error")
            return render_template("contact.html")

        # التحقق من الإيميل (أحرف إنجليزية فقط)
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            flash("عنوان البريد الإلكتروني غير صحيح أو يحتوي على أحرف غير إنجليزية. يرجى استخدام الأحرف الإنجليزية فقط.", "error")
            return render_template("contact.html")

        # التحقق من رقم الهاتف (إذا تم إدخاله)
        if phone:
            phone_pattern = r'^[0-9+\s\-()]+$'
            if not re.match(phone_pattern, phone):
                flash("رقم الهاتف يحتوي على أحرف غير مسموحة. يُسمح بالأرقام والرموز (+، -، مسافة، أقواس) فقط.", "error")
                return render_template("contact.html")

        # حفظ الرسالة في ملف مؤقت وإرسال إيميل
        try:
            # إنشاء محتوى الإيميل
            email_content = f"""
رسالة جديدة من موقع ميتاق للاستشارات الهندسية
================================================

معلومات المرسل:
الاسم: {name}
البريد الإلكتروني: {email}
رقم الهاتف: {phone}
الموضوع: {subject}

الرسالة:
{message}

---
تم الإرسال في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
من موقع: ميتاق للاستشارات الهندسية
            """

            # حفظ الرسالة في ملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"message_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(email_content)

            print(f"تم حفظ الرسالة في ملف: {filename}")
            print("محتوى الرسالة:")
            print(email_content)

            # محاولة إرسال إيميل بسيط
            print("🚀 محاولة إرسال الإيميل...")
            email_sent = send_simple_email(name, email, phone, subject, message)

            if email_sent:
                flash("تم إرسال رسالتك بنجاح! سنقوم بالرد عليك في أقرب وقت ممكن.", "success")
                print("✅ تم إرسال الإيميل وحفظ الرسالة بنجاح")
            else:
                flash("تم استلام رسالتك بنجاح. سنقوم بالتواصل معك قريباً.", "success")
                print("⚠️ لم يتم إرسال الإيميل لكن تم حفظ الرسالة")

        except Exception as e:
            print(f"خطأ في معالجة الرسالة: {e}")
            flash("تم استلام رسالتك بنجاح. سنقوم بالتواصل معك قريباً.", "success")

        return redirect(url_for("main.contact"))
    return render_template("contact.html")
