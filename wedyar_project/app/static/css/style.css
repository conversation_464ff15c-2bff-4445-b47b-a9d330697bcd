/* MYTHAQ Ultra Professional Styling - Updated v2.0 */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css');

/* ===== MYTHAQ LOGO STYLES ===== */
.mythaq-logo {
  height: 40px;
  width: auto;
  margin-right: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: brightness(1.1) contrast(1.1);
}

.mythaq-logo:hover {
  transform: scale(1.08) translateY(-1px);
  filter: brightness(1.3) contrast(1.2);
}

.navbar-brand {
  display: flex !important;
  align-items: center !important;
  text-decoration: none !important;
  padding: 0.5rem 0 !important;
}

.navbar-brand span {
  font-weight: 800;
  font-size: 1.8rem;
  margin-left: 8px;
  background: var(--gold-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

/* Footer Logo Styles */
.footer-logo {
  height: 32px;
  width: auto;
  margin-right: 8px;
  filter: brightness(1.2) contrast(1.1);
}

/* Hero Logo Styles */
.hero-logo {
  width: 100%;
  max-width: 380px;
  height: auto;
  filter: brightness(1.2) contrast(1.15) saturate(1.1);
}

/* Professional Hero Logo Styles for MYTHAQ */
.mythaq-professional-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  animation: fadeInUp 1s ease-out;
}

.hero-logo-image {
  max-width: 450px;
  width: 100%;
  height: auto;
  filter: drop-shadow(0 20px 40px rgba(203, 161, 53, 0.3));
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.05);
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(203, 161, 53, 0.2);
  position: relative;
  overflow: hidden;
}

.hero-logo-image::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
  pointer-events: none;
}

.hero-logo-image:hover {
  transform: translateY(-10px) scale(1.05);
  filter: drop-shadow(0 30px 60px rgba(203, 161, 53, 0.5));
  border-color: rgba(203, 161, 53, 0.4);
}

.logo-subtitle {
  color: #CBA135;
  font-weight: 600;
  font-size: 1.5rem;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  letter-spacing: 1px;
  font-family: 'Inter', sans-serif;
  animation: fadeInUp 1s ease-out 0.3s both;
}

.logo-tagline {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  font-style: italic;
  margin-bottom: 0;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
  font-family: 'Playfair Display', serif;
  animation: fadeInUp 1s ease-out 0.6s both;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design for Hero Logo */
@media (max-width: 768px) {
  .hero-logo-image {
    max-width: 350px;
    padding: 15px;
  }

  .logo-subtitle {
    font-size: 1.3rem;
  }

  .logo-tagline {
    font-size: 1rem;
  }

  .mythaq-professional-logo {
    padding: 30px 15px;
  }
}

@media (max-width: 480px) {
  .hero-logo-image {
    max-width: 280px;
    padding: 10px;
  }

  .logo-subtitle {
    font-size: 1.1rem;
  }

  .logo-tagline {
    font-size: 0.9rem;
  }

  .mythaq-professional-logo {
    padding: 20px 10px;
  }
}

/* Fallback Logo Design */
.fallback-logo-design {
  text-align: center;
  padding: 40px 20px;
  background: none !important;
  border-radius: 15px;
  box-shadow: none !important;
  border: none !important;
  max-width: 450px;
  margin: 0 auto;
}

.fallback-logo-design img {
  border: 3px solid white;
  border-radius: 10px;
  max-width: 100%;
  height: auto;
}

.fallback-title {
  color: white;
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  letter-spacing: 2px;
  font-family: 'Inter', sans-serif;
}

.fallback-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.2rem;
  font-weight: 500;
  margin-bottom: 0;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
  font-family: 'Cairo', sans-serif;
}

.logo-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Founder Section Styles */
.founder-section {
  padding: 20px;
}

.founder-image-container {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
}

.founder-image {
  width: 250px;
  height: 250px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #CBA135;
  box-shadow: 0 15px 35px rgba(203, 161, 53, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.05);
}

.founder-image:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 25px 50px rgba(203, 161, 53, 0.5);
  border-color: #D4AF37;
}

.founder-placeholder {
  width: 250px;
  height: 250px;
  border-radius: 50%;
  background: linear-gradient(135deg, #CBA135 0%, #D4AF37 50%, #B8941F 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  border: 4px solid rgba(203, 161, 53, 0.5);
  box-shadow: 0 15px 35px rgba(203, 161, 53, 0.3);
}

.founder-info {
  text-align: center;
  max-width: 400px;
  margin: 0 auto;
}

.founder-name {
  color: #CBA135;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
  font-family: 'Inter', sans-serif;
}

.founder-title {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 15px;
  font-style: italic;
}

.founder-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 0;
}

/* Responsive Design for Founder Section */
@media (max-width: 768px) {
  .founder-image,
  .founder-placeholder {
    width: 200px;
    height: 200px;
  }

  .founder-name {
    font-size: 1.3rem;
  }

  .founder-title {
    font-size: 1rem;
  }

  .founder-description {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .founder-image,
  .founder-placeholder {
    width: 180px;
    height: 180px;
  }

  .founder-name {
    font-size: 1.2rem;
  }

  .founder-title {
    font-size: 0.95rem;
  }

  .founder-description {
    font-size: 0.85rem;
  }
}
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  animation: heroLogoFloat 10s ease-in-out infinite;
  drop-shadow: 0 12px 30px rgba(203, 161, 53, 0.4);
}

.hero-logo:hover {
  transform: scale(1.06) translateY(-10px);
  filter: brightness(1.5) contrast(1.4) saturate(1.3);
  drop-shadow: 0 20px 40px rgba(203, 161, 53, 0.6);
}

@keyframes heroLogoFloat {
  0%, 100% {
    transform: translateY(0px);
    filter: brightness(1.2) contrast(1.15) saturate(1.1);
  }
  50% {
    transform: translateY(-15px);
    filter: brightness(1.3) contrast(1.2) saturate(1.2);
  }
}

/* MYTHAQ Logo Container */
.mythaq-logo-container {
  max-width: 350px;
  margin: 0 auto;
  animation: logoFloat 8s ease-in-out infinite;
}

.logo-elements {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
  margin-bottom: 20px;
}

/* Shield with Compass */
.shield-compass {
  position: relative;
}

.shield {
  width: 80px;
  height: 80px;
  border: 3px solid #CBA135;
  border-radius: 8px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.compass {
  position: relative;
  width: 50px;
  height: 50px;
}

.compass-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: #CBA135;
  border-radius: 50%;
}

.compass-arms .arm {
  position: absolute;
  background: #CBA135;
}

.compass-arms .vertical {
  top: 5px;
  left: 50%;
  transform: translateX(-50%);
  width: 3px;
  height: 40px;
}

.compass-arms .horizontal {
  top: 50%;
  left: 5px;
  transform: translateY(-50%);
  width: 40px;
  height: 3px;
}

.compass-points .point {
  position: absolute;
  background: #CBA135;
}

.compass-points .north {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 8px solid #CBA135;
  background: transparent;
}

.compass-points .east {
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-left: 8px solid #CBA135;
  background: transparent;
}

/* Buildings */
.buildings {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.building {
  border: 3px solid #CBA135;
  position: relative;
}

.building.tall {
  width: 25px;
  height: 80px;
}

.building.medium {
  width: 20px;
  height: 60px;
}

.building.short {
  width: 18px;
  height: 40px;
}

.windows {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2px;
  padding: 3px;
  height: calc(100% - 10px);
}

.building.tall .windows {
  grid-template-columns: 1fr 1fr 1fr;
}

.window {
  background: #CBA135;
  height: 8px;
}

.door {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 8px;
  background: #CBA135;
}

/* Company Name */
.company-name {
  text-align: center;
}

.arabic-name {
  font-size: 2.5rem;
  font-weight: 800;
  background: var(--gold-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  font-family: 'Cairo', Arial, sans-serif;
}

.tagline {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  margin: 5px 0 0 0;
  font-family: 'Inter', Arial, sans-serif;
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Hover Effects */
.mythaq-logo-container:hover {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

.mythaq-logo-container:hover .shield {
  border-color: #D4AF37;
  box-shadow: 0 0 20px rgba(203, 161, 53, 0.5);
}

.mythaq-logo-container:hover .building {
  border-color: #D4AF37;
}

.mythaq-logo-container:hover .window,
.mythaq-logo-container:hover .door,
.mythaq-logo-container:hover .compass-center,
.mythaq-logo-container:hover .compass-arms .arm {
  background: #D4AF37;
}

.mythaq-logo-container:hover .compass-points .north {
  border-bottom-color: #D4AF37;
}

.mythaq-logo-container:hover .compass-points .east {
  border-left-color: #D4AF37;
}

/* About Image Placeholder */
.about-image-placeholder {
  width: 100%;
  height: 300px;
  background: rgba(255,255,255,0.1);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(203, 161, 53, 0.3);
}

.about-image-content {
  text-align: center;
}

/* Project Image Placeholder */
.project-image-placeholder {
  width: 100%;
  height: 200px;
  background: rgba(255,255,255,0.1);
  border-radius: 15px 15px 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border-bottom: 2px solid rgba(203, 161, 53, 0.3);
}

.project-image-content {
  text-align: center;
}

/* Contact Items */
.contact-item {
  padding: 15px 0;
  border-bottom: 1px solid rgba(203, 161, 53, 0.2);
}

.contact-item:last-child {
  border-bottom: none;
}

/* Smooth Scrolling for Navigation */
html {
  scroll-behavior: smooth;
}

/* Section Spacing */
section {
  scroll-margin-top: 80px;
}

/* Hero Image Container */
.hero-image-container {
  max-width: 600px;
  margin: 0 auto;
}

.hero-image {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 3px solid rgba(203, 161, 53, 0.3);
}

.hero-image:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 30px 60px rgba(203, 161, 53, 0.4) !important;
}

.hero-image-caption {
  animation: fadeInUp 1s ease-out 0.5s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* About Image Placeholder */
.about-image-placeholder {
  width: 100%;
  height: 300px;
  background: rgba(255,255,255,0.1);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(203, 161, 53, 0.3);
}

.about-image-content {
  text-align: center;
}

/* Project Image Placeholder */
.project-image-placeholder {
  width: 100%;
  height: 200px;
  background: rgba(255,255,255,0.1);
  border-radius: 15px 15px 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border-bottom: 2px solid rgba(203, 161, 53, 0.3);
}

.project-image-content {
  text-align: center;
}

/* Team Member Photo */
.team-member-photo {
  display: flex;
  justify-content: center;
  align-items: center;
}

.photo-placeholder {
  width: 180px;
  height: 180px;
  background: rgba(255,255,255,0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 3px solid rgba(203, 161, 53, 0.5);
}

/* Contact Form Styles */
.form-control:focus,
.form-select:focus {
  border-color: #CBA135 !important;
  box-shadow: 0 0 0 0.2rem rgba(203, 161, 53, 0.25) !important;
}

.form-control::placeholder {
  color: rgba(255,255,255,0.5) !important;
}

.form-select option {
  background-color: #2d2d2d !important;
  color: white !important;
}

/* Contact Items */
.contact-item {
  padding: 15px 0;
  border-bottom: 1px solid rgba(203, 161, 53, 0.2);
}

.contact-item:last-child {
  border-bottom: none;
}

/* Smooth Scrolling for Navigation */
html {
  scroll-behavior: smooth;
}

/* Section Spacing */
section {
  scroll-margin-top: 80px;
}

:root {
  /* ===== MYTHAQ VISUAL IDENTITY COLORS ===== */
  /* الألوان الرسمية لشركة ميتاق */
  --primary-color: #2F2F2F;        /* رمادي داكن أنيق - اللون الرئيسي */
  --secondary-color: #CBA135;      /* ذهبي فاخر - اللون الثانوي */
  --accent-white: #FFFFFF;         /* أبيض نقي - لون مساعد */
  --background-light: #F5F5F5;     /* رمادي فاتح - لون الخلفية */

  /* الألوان الأصلية للتوافق مع الكود الحالي */
  --primary-gold: #CBA135;
  --secondary-gold: #D4AF37;
  --dark-gold: #B8860B;
  --light-gold: #F4E4BC;
  --gold-gradient: linear-gradient(135deg, #CBA135 0%, #D4AF37 50%, #B8860B 100%);

  /* تدرجات الهوية البصرية */
  --mythaq-gradient: linear-gradient(135deg, #2F2F2F 0%, #CBA135 100%);
  --mythaq-dark-gradient: linear-gradient(135deg, #2F2F2F 0%, #1A1A1A 100%);
  --mythaq-light-gradient: linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 100%);

  /* الألوان الثانوية */
  --navy-blue: #2F2F2F;
  --dark-navy: #1A1A1A;
  --medium-navy: #334155;
  --light-navy: #475569;
  --accent-blue: #3B82F6;

  /* الرماديات المحدثة */
  --charcoal: #1A1A1A;
  --dark-gray: #2F2F2F;
  --medium-gray: #4A4A4A;
  --light-gray: #F5F5F5;
  --off-white: #FEFEFE;
  --pure-white: #FFFFFF;

  /* Shadows */
  --shadow-md: 0 8px 25px rgba(0,0,0,0.1);
  --shadow-lg: 0 15px 35px rgba(0,0,0,0.15);
  --shadow-xl: 0 25px 50px rgba(0,0,0,0.2);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--navy-blue) 0%, var(--dark-navy) 100%);
  --gold-gradient: linear-gradient(135deg, var(--primary-gold) 0%, var(--secondary-gold) 100%);

  /* Typography */
  --font-primary: 'Cairo', 'Inter', sans-serif;
  --font-display: 'Playfair Display', 'Cairo', serif;
  --font-body: 'Inter', 'Cairo', sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  background: var(--light-gray);
  color: var(--dark-gray);
  line-height: 1.7;
  overflow-x: hidden;
  font-weight: 400;
  letter-spacing: 0.01em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  padding-top: 90px; /* مساحة للـ navbar الثابت */
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--gold-gradient);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--dark-gold);
}



/* Ultra Premium Navbar - Fixed Position */
.navbar {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-md);
  padding: 1.2rem 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1030;
}

.navbar.scrolled {
  padding: 0.8rem 0;
  box-shadow: var(--shadow-lg);
  background: rgba(255, 255, 255, 0.98) !important;
}

.navbar-brand {
  font-family: var(--font-display);
  font-weight: 800;
  font-size: 2.2rem;
  background: var(--gold-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  letter-spacing: -0.02em;
}

.navbar-brand:hover {
  transform: translateY(-1px);
  filter: brightness(1.1);
}

.nav-link {
  font-family: var(--font-body);
  font-weight: 500;
  color: var(--dark-gray) !important;
  margin: 0 0.3rem;
  padding: 0.7rem 1.2rem !important;
  border-radius: 30px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.02em;
}



.nav-link:hover {
  background: var(--gold-gradient);
  color: white !important;
  transform: translateY(-2px);
}

/* Premium Gold Theme */
.gold-text {
  background: var(--gold-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  position: relative;
}



/* Ultra Premium Buttons */
.btn-gold, .btn-primary {
  background: var(--gold-gradient) !important;
  border: none !important;
  color: white !important;
  padding: 16px 40px !important;
  border-radius: 50px !important;
  font-family: var(--font-primary) !important;
  font-weight: 700 !important;
  font-size: 1.1rem !important;
  text-transform: uppercase !important;
  letter-spacing: 1.5px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: none !important;
  position: relative !important;
  overflow: hidden !important;
  cursor: pointer !important;
  border: 2px solid transparent !important;
  min-width: 200px !important;
  text-decoration: none !important;
  display: inline-block !important;
  text-align: center !important;
}



.btn-gold:hover, .btn-primary:hover {
  transform: translateY(-4px) scale(1.02) !important;
  box-shadow: none !important;
  color: white !important;
  border-color: rgba(255,255,255,0.3) !important;
  background: linear-gradient(135deg, #e6c547 0%, #f4e4a6 50%, #d4af37 100%) !important;
}



.btn-gold:active, .btn-primary:active {
  transform: translateY(-2px) scale(0.98);
  transition: all 0.1s ease;
}

.btn-outline-gold:active, .btn-outline-primary:active, .btn-outline-light:active {
  transform: translateY(-1px) scale(0.98);
  transition: all 0.1s ease;
}

/* Outline Gold Button */
.btn-outline-gold, .btn-outline-primary {
  background: rgba(212, 175, 55, 0.05) !important;
  border: 3px solid var(--primary-gold) !important;
  color: var(--primary-gold) !important;
  padding: 16px 40px !important;
  border-radius: 50px !important;
  font-family: var(--font-primary) !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 1.5px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  min-width: 200px !important;
  text-decoration: none !important;
  display: inline-block !important;
  text-align: center !important;
  backdrop-filter: blur(10px) !important;
  font-size: 1.1rem !important;
}

.btn-outline-gold::before, .btn-outline-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gold-gradient);
  transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.btn-outline-gold:hover::before, .btn-outline-primary:hover::before {
  left: 0;
}

.btn-outline-gold:hover, .btn-outline-primary:hover {
  color: white;
  border-color: var(--secondary-gold);
  transform: translateY(-3px) scale(1.02);
  box-shadow: none;
  background: var(--gold-gradient);
}

/* White Outline Button for Hero Section */
.btn-outline-light {
  background: rgba(255,255,255,0.1) !important;
  border: 3px solid rgba(255,255,255,0.9) !important;
  color: white !important;
  padding: 16px 40px !important;
  border-radius: 50px !important;
  font-family: var(--font-primary) !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 1.5px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  min-width: 200px !important;
  text-decoration: none !important;
  display: inline-block !important;
  text-align: center !important;
  backdrop-filter: blur(10px) !important;
  font-size: 1.1rem !important;
}

.btn-outline-light::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255,255,255,0.2);
  transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.btn-outline-light:hover::before {
  left: 0;
}

.btn-outline-light:hover {
  color: var(--navy-blue);
  background: rgba(255,255,255,0.95);
  border-color: white;
  transform: translateY(-3px) scale(1.02);
  box-shadow: none;
  font-weight: 700;
}

/* Ultra Premium Hero Section */
.hero-section {
  background: var(--gradient-primary);
  color: white;
  padding: 50px 0 100px; /* تقليل padding العلوي */
  position: relative;
  overflow: hidden;
  min-height: calc(100vh - 90px); /* تعديل الارتفاع لحساب الـ navbar */
  display: flex;
  align-items: center;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(201,169,97,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(212,175,55,0.1) 0%, transparent 50%),
    linear-gradient(135deg, rgba(15,23,42,0.8) 0%, rgba(30,41,59,0.9) 100%);
  z-index: -1;
  pointer-events: none;
}

.hero-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to top, var(--light-gray), transparent);
  z-index: -1;
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 10;
}

.hero-section .display-4 {
  font-family: var(--font-display);
  font-weight: 800;
  font-size: clamp(2.5rem, 5vw, 4rem);
  line-height: 1.1;
  margin-bottom: 2rem;
  text-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.hero-section .lead {
  font-family: var(--font-body);
  font-size: clamp(1.1rem, 2.5vw, 1.4rem);
  font-weight: 300;
  letter-spacing: 0.5px;
  margin-bottom: 1.5rem;
  opacity: 0.95;
}

.hero-section .fs-5 {
  font-family: var(--font-primary);
  font-size: clamp(1rem, 2vw, 1.2rem);
  font-weight: 400;
  line-height: 1.8;
  opacity: 0.9;
}

/* Enhanced Hero Buttons - CRITICAL FIX */
.hero-section .btn,
.hero-section .btn-lg,
.hero-section a.btn,
.hero-section a.btn-lg {
  position: relative !important;
  z-index: 1000 !important;
  font-weight: 700 !important;
  letter-spacing: 0.5px !important;
  text-transform: uppercase !important;
  border-radius: 50px !important;
  padding: 20px 45px !important;
  font-size: 18px !important;
  line-height: 1.5 !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border: 3px solid transparent !important;
  text-decoration: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 250px !important;
  cursor: pointer !important;
  overflow: hidden !important;
  pointer-events: auto !important;
  user-select: none !important;
  margin: 10px !important;
  opacity: 1 !important;
  visibility: visible !important;
}



.hero-section .btn-gold,
.hero-section .btn-gold.btn-lg {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%) !important;
  color: #000000 !important;
  border: 3px solid #FFD700 !important;
  box-shadow: none !important;
  font-weight: 800 !important;
  opacity: 1 !important;
  visibility: visible !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

.hero-section .btn-gold:hover,
.hero-section .btn-gold.btn-lg:hover {
  background: linear-gradient(135deg, #FFFF00 0%, #FFD700 50%, #FFA500 100%) !important;
  color: #000000 !important;
  transform: translateY(-4px) scale(1.08) !important;
  box-shadow: none !important;
  border-color: #FFFF00 !important;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.4) !important;
}

.hero-section .btn-outline-light,
.hero-section .btn-outline-light.btn-lg {
  background: rgba(255, 255, 255, 0.15) !important;
  color: #FFFFFF !important;
  border: 3px solid #FFFFFF !important;
  backdrop-filter: blur(10px);
  font-weight: 700 !important;
  opacity: 1 !important;
  visibility: visible !important;
  text-shadow: 1px 1px 3px rgba(0,0,0,0.5) !important;
  box-shadow: none !important;
}

.hero-section .btn-outline-light:hover,
.hero-section .btn-outline-light.btn-lg:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  color: #FFFFFF !important;
  transform: translateY(-4px) scale(1.08) !important;
  box-shadow: none !important;
  border-color: #FFFFFF !important;
  backdrop-filter: blur(15px);
  text-shadow: 2px 2px 4px rgba(0,0,0,0.6) !important;
}

/* Force button visibility and functionality */
.hero-section .d-flex .btn,
.hero-section .d-flex .btn-lg {
  opacity: 1 !important;
  visibility: visible !important;
  pointer-events: auto !important;
  position: relative !important;
  z-index: 1000 !important;
}

.hero-section .d-flex {
  position: relative;
  z-index: 100;
}

/* Ensure hero section doesn't block interactions */
.hero-section {
  pointer-events: none;
}

.hero-section * {
  pointer-events: auto;
}

/* Override Bootstrap btn-lg specifically for hero section */
.hero-section .btn-lg {
  padding: 20px 45px !important;
  font-size: 18px !important;
  border-radius: 50px !important;
  line-height: 1.5 !important;
}

/* Ensure buttons are always clickable */
.hero-section a.btn,
.hero-section a.btn-lg {
  text-decoration: none !important;
  color: inherit !important;
}

/* Hero Image Placeholder */
.hero-image-placeholder {
  width: 100%;
  max-width: 500px;
  height: 400px;
  background: var(--gold-gradient);
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  margin: 0 auto;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 3px solid rgba(255,255,255,0.2);
}

.hero-image-placeholder:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}



.hero-image-content {
  text-align: center;
  color: white;
  position: relative;
  z-index: 2;
}

.hero-image-content i {
  opacity: 0.9;
  margin-bottom: 1rem;
}

.hero-image-content h3 {
  font-family: var(--font-display);
  font-weight: 800;
  font-size: 3rem;
  margin-bottom: 0.5rem;
  text-shadow: 0 4px 15px rgba(0,0,0,0.4);
  letter-spacing: 2px;
}

.hero-image-content p {
  font-family: var(--font-body);
  font-size: 1.4rem;
  opacity: 0.95;
  margin: 0;
  font-weight: 500;
  text-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.hero-image-content i {
  opacity: 0.9;
  margin-bottom: 1.5rem;
  filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
}

/* Ultra Premium Cards */
.card {
  border: none;
  border-radius: 24px;
  box-shadow: var(--shadow-md);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  background: var(--pure-white);
  position: relative;
  backdrop-filter: blur(10px);
}



.card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.card-body {
  padding: 2rem;
  position: relative;
}

.card-title {
  font-family: var(--font-display);
  font-weight: 700;
  font-size: 1.4rem;
  color: var(--dark-gray);
  margin-bottom: 1rem;
  position: relative;
}

.card-text {
  font-family: var(--font-body);
  color: var(--medium-gray);
  line-height: 1.7;
  font-size: 0.95rem;
}

.card-img-top {
  height: 280px;
  object-fit: cover;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover .card-img-top {
  transform: scale(1.08);
  filter: brightness(1.1) saturate(1.2);
}

/* Feature Cards */
.feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, var(--light-gold), var(--primary-gold));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}



.feature-icon i {
  position: relative;
  z-index: 2;
  color: white;
  transition: all 0.4s ease;
}

.card:hover .feature-icon i {
  transform: scale(1.1);
}

/* Ultra Premium Services List */
.list-group-item {
  border: none;
  border-radius: 20px !important;
  margin-bottom: 15px;
  background: var(--pure-white);
  box-shadow: var(--shadow-md);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 25px 30px;
  position: relative;
  overflow: hidden;
  font-family: var(--font-body);
  font-weight: 500;
  font-size: 1.1rem;
  border-left: 4px solid transparent;
}

.list-group-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gold-gradient);
  transition: left 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.list-group-item::after {
  content: '\f061';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  position: absolute;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 3;
  color: white;
}

.list-group-item:hover {
  transform: translateX(15px) translateY(-2px);
  box-shadow: var(--shadow-xl);
  color: white;
  border-left-color: var(--secondary-gold);
}

.list-group-item:hover::before {
  left: 0;
}

.list-group-item:hover::after {
  opacity: 1;
  right: 20px;
}

.list-group-item span {
  position: relative;
  z-index: 2;
}

/* Ultra Premium Footer */
footer {
  background: var(--gradient-primary);
  color: white !important;
  padding: 60px 0 30px;
  margin-top: 80px;
  position: relative;
  overflow: hidden;
}



footer h5, footer h6 {
  font-family: var(--font-display);
  font-weight: 700;
  position: relative;
  z-index: 2;
}

footer p, footer li {
  font-family: var(--font-body);
  position: relative;
  z-index: 2;
  opacity: 0.9;
}



/* Team Page Styles */
.team-card {
  background: var(--pure-white);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  height: 100%;
}

.team-card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.team-image {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.team-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.team-card:hover .team-image img {
  transform: scale(1.1);
}



.team-content {
  padding: 2rem;
}

.team-name {
  font-family: var(--font-display);
  font-weight: 700;
  font-size: 1.4rem;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.team-position {
  font-family: var(--font-primary);
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--primary-gold);
  margin-bottom: 0.5rem;
}

.team-specialization {
  font-family: var(--font-body);
  font-size: 0.95rem;
  color: var(--medium-gray);
  margin-bottom: 1.5rem;
  font-style: italic;
}

.team-details {
  margin-bottom: 1.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.8rem;
  font-family: var(--font-body);
  font-size: 0.9rem;
  color: var(--medium-gray);
}

.detail-item i {
  width: 20px;
  margin-left: 10px;
  font-size: 0.9rem;
}

.team-description {
  font-family: var(--font-body);
  color: var(--medium-gray);
  line-height: 1.6;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
}

.team-skills {
  margin-top: 1.5rem;
}

.skills-title {
  font-family: var(--font-primary);
  font-weight: 600;
  color: var(--dark-gray);
  margin-bottom: 1rem;
  font-size: 0.95rem;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.skill-tag {
  background: linear-gradient(135deg, var(--light-gold), var(--primary-gold));
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  font-family: var(--font-body);
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.8s ease forwards;
}

/* Responsive Design */
@media (max-width: 768px) {
  .navbar-brand {
    font-size: 1.5rem;
  }

  .hero-section {
    padding: 60px 0;
  }

  .btn-gold, .btn-primary, .btn-outline-gold, .btn-outline-primary, .btn-outline-light {
    padding: 14px 30px;
    font-size: 1rem;
    min-width: 160px;
  }

  .hero-image-placeholder {
    height: 300px;
    max-width: 400px;
  }

  .hero-image-content h3 {
    font-size: 2.2rem;
  }

  .hero-image-content p {
    font-size: 1.1rem;
  }

  /* Team Page Mobile Styles */
  .team-card {
    margin-bottom: 2rem;
  }

  .team-image {
    height: 250px;
  }

  .team-content {
    padding: 1.5rem;
  }

  .team-name {
    font-size: 1.2rem;
  }

  .team-position {
    font-size: 1rem;
  }

  .team-description {
    font-size: 0.85rem;
  }

  .detail-item {
    font-size: 0.8rem;
  }

  .skill-tag {
    font-size: 0.75rem;
    padding: 4px 8px;
  }


}

/* Contact Form */
.form-control {
  border-radius: 15px;
  border: 2px solid #e2e8f0;
  padding: 15px 20px;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
}

/* Ultra Premium Social Media Icons */
.social-icons a {
  display: inline-block;
  width: 55px;
  height: 55px;
  background: var(--gold-gradient);
  color: white;
  text-align: center;
  line-height: 55px;
  border-radius: 50%;
  margin: 0 8px;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}



.social-icons a i {
  position: relative;
  z-index: 2;
  transition: all 0.4s ease;
}

.social-icons a:hover {
  transform: translateY(-8px) scale(1.15) rotate(5deg);
  box-shadow: var(--shadow-xl);
  color: white;
}

.social-icons a:hover i {
  transform: scale(1.2);
}

/* Premium Form Styling */
.form-control {
  border-radius: 15px;
  border: 2px solid #e2e8f0;
  padding: 15px 20px;
  font-family: var(--font-body);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--pure-white);
  box-shadow: var(--shadow-sm);
}

.form-control:focus {
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 0.25rem rgba(201, 169, 97, 0.25);
  transform: translateY(-2px);
}

.form-label {
  font-family: var(--font-body);
  font-weight: 600;
  color: var(--dark-gray);
  margin-bottom: 0.75rem;
}

/* Premium Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.floating {
  animation: float 3s ease-in-out infinite;
}

/* Language Switcher Styles - More Visible */
.language-btn {
  border: 3px solid rgba(255, 215, 0, 0.8) !important;
  color: #FFD700 !important;
  background: rgba(255, 215, 0, 0.15) !important;
  backdrop-filter: blur(10px);
  border-radius: 30px !important;
  padding: 14px 28px !important;
  font-weight: 600;
  transition: all 0.3s ease;
  font-size: 1rem;
  box-shadow: none;
  text-shadow: 0 1px 3px rgba(0,0,0,0.3);
  position: relative;
  overflow: hidden;
  min-width: 130px;
  z-index: 1000;
  letter-spacing: 0.5px;
}



.language-btn:hover {
  background: rgba(255, 215, 0, 0.25) !important;
  border-color: #FFD700 !important;
  transform: translateY(-3px) scale(1.05);
  box-shadow: none;
  color: #FFFF00 !important;
}

.language-dropdown {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(15px);
  border: none !important;
  border-radius: 15px !important;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  padding: 10px 0;
  margin-top: 10px;
  min-width: 150px;
}

.language-dropdown .dropdown-item {
  padding: 10px 20px;
  font-weight: 500;
  color: var(--dark-gray) !important;
  transition: all 0.3s ease;
  border-radius: 10px;
  margin: 2px 8px;
}

.language-dropdown .dropdown-item:hover {
  background: var(--gold-gradient) !important;
  color: white !important;
  transform: translateX(5px);
}

.language-dropdown .dropdown-item.active {
  background: var(--gold-gradient) !important;
  color: white !important;
  font-weight: 600;
}

.language-dropdown .dropdown-item i {
  width: 20px;
  text-align: center;
}

/* RTL Support for Arabic */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .navbar-nav {
  margin-right: auto;
  margin-left: 0;
}

[dir="rtl"] .dropdown-menu-end {
  right: auto;
  left: 0;
}

[dir="rtl"] .me-1, [dir="rtl"] .me-2 {
  margin-right: 0;
  margin-left: 0.25rem;
}

[dir="rtl"] .language-dropdown .dropdown-item:hover {
  transform: translateX(-5px);
}

/* Language Button - No Animation */

/* Mobile Responsive for Language Button */
@media (max-width: 768px) {
  .language-btn {
    padding: 12px 22px !important;
    font-size: 0.95rem;
    min-width: 120px;
  }

  .language-dropdown {
    min-width: 140px;
  }

  .navbar-toggler {
    margin-left: 10px;
  }
}

/* Extra visibility for language button */
.language-btn {
  border-style: solid !important;
  border-width: 3px !important;
  outline: none !important;
}

.language-btn:focus {
  box-shadow:
    0 6px 20px rgba(255, 215, 0, 0.4),
    0 0 0 0.25rem rgba(255, 215, 0, 0.25) !important;
}

/* Ensure language button is always visible */
.navbar .language-btn {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  white-space: nowrap !important;
}

/* Fix for navbar collapse */
@media (max-width: 991.98px) {
  .navbar-collapse .d-flex {
    margin-top: 1rem;
    justify-content: center;
  }
}

/* ===== WHATSAPP FLOATING BUTTON ===== */
.whatsapp-float {
  position: fixed;
  bottom: 30px;
  left: 30px;
  z-index: 1000;
  animation: pulse 2s infinite;
}

.whatsapp-btn {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #25d366, #128c7e);
  color: white;
  padding: 18px 24px;
  border-radius: 60px;
  text-decoration: none;
  box-shadow: 0 6px 25px rgba(37, 211, 102, 0.5);
  transition: all 0.3s ease;
  font-weight: 700;
  font-size: 16px;
  min-width: 80px;
  justify-content: center;
}

.whatsapp-btn:hover {
  background: linear-gradient(135deg, #128c7e, #25d366);
  color: white;
  transform: translateY(-4px) scale(1.1);
  box-shadow: 0 8px 30px rgba(37, 211, 102, 0.7);
  text-decoration: none;
}

.whatsapp-btn i {
  font-size: 32px;
  margin-right: 12px;
}

.whatsapp-text {
  display: none;
  white-space: nowrap;
}

/* إظهار النص عند التمرير */
.whatsapp-btn:hover .whatsapp-text {
  display: inline;
  animation: slideIn 0.3s ease;
}

/* تحريك النبضة */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* تحريك النص */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* تصميم متجاوب للموبايل */
@media (max-width: 768px) {
  .whatsapp-float {
    bottom: 20px;
    left: 20px;
  }

  .whatsapp-btn {
    padding: 15px 18px;
    min-width: 70px;
  }

  .whatsapp-btn i {
    font-size: 28px;
    margin-right: 0;
  }

  .whatsapp-text {
    display: none !important;
  }
}

/* الزر يبقى في الجانب الأيسر دائماً */
.whatsapp-float {
  left: 30px !important;
}

@media (max-width: 768px) {
  .whatsapp-float {
    left: 20px !important;
  }
}

/* ===== CONTACT FORM IMPROVEMENTS ===== */
/* حقول الإيميل والهاتف - اتجاه من اليسار إلى اليمين */
input[type="email"],
input[type="tel"] {
  direction: ltr !important;
  text-align: left !important;
  font-family: 'Arial', sans-serif !important;
}

/* تحسين placeholder للحقول الخاصة */
input[type="email"]::placeholder,
input[type="tel"]::placeholder {
  direction: ltr !important;
  text-align: left !important;
  color: #6c757d !important;
  font-style: italic;
}

/* تأكيد اتجاه النص حتى في البيئة العربية */
[dir="rtl"] input[type="email"],
[dir="rtl"] input[type="tel"] {
  direction: ltr !important;
  text-align: left !important;
}

[dir="rtl"] input[type="email"]::placeholder,
[dir="rtl"] input[type="tel"]::placeholder {
  direction: ltr !important;
  text-align: left !important;
}
