#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# اختبار دالة الاتصال
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from flask import request

app = create_app()

with app.app_context():
    with app.test_client() as client:
        # محاولة إرسال POST request
        print("🔄 بدء اختبار إرسال POST request...")
        response = client.post('/contact', data={
            'name': 'اختبار',
            'email': '<EMAIL>',
            'phone': '123456789',
            'subject': 'اختبار',
            'message': 'رسالة اختبار'
        })
    
    print(f"Response status: {response.status_code}")
    print(f"Response headers: {response.headers}")
    if response.status_code == 302:
        print(f"Redirect location: {response.location}")
    else:
        print(f"Response data: {response.data}")
